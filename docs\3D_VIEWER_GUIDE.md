# 3D Model Viewer Integration Guide

## Tổng quan

Hệ thống đã đượ<PERSON> tích hợp với 3D Model Viewer sử dụng Three.js để hiển thị file .obj trực tiếp trên giao diện web, thay thế cho việc sử dụng ứng dụng desktop Tolery/gui_step.py.

## Tính năng chính

### 1. Hiển thị 3D Model trực tiếp trên web
- Tự động load và hiển thị file .obj khi được tạo ra từ FreeCAD
- Hỗ trợ zoom, rotate, pan bằng chuột
- Hiển thị thông tin model (vertices, faces, tên file)

### 2. Giao diện tương tác
- **Reset Camera**: Đưa camera về vị trí mặc định
- **Toggle Wireframe**: Chuyển đổi giữa chế độ solid và wireframe
- **Download OBJ**: Tải xuống file .obj
- **Fullscreen**: <PERSON><PERSON> ở chế độ toàn màn hình

### 3. <PERSON><PERSON><PERSON> hợp với workflow hiện tại
- Tự động hiển thị model khi CAD generation hoàn thành
- Hiển thị code có thể thu gọn/mở rộng
- Giữ nguyên chức năng STEP viewer cho các file .step

## Cấu trúc file

```
├── static/js/
│   ├── 3d-viewer.js          # Class ModelViewer3D chính
│   └── main.js               # Tích hợp với chat interface
├── templates/
│   ├── index.html            # Giao diện chính với 3D viewer
│   └── test-3d-viewer.html   # Trang test riêng biệt
├── src/api/
│   └── main.py               # API endpoints cho serving files
└── outputs/obj/              # Thư mục chứa file .obj
```

## API Endpoints

### 1. `/api/3d-viewer/{file_path:path}`
- **Mục đích**: Serve file .obj cho 3D viewer (không có download headers)
- **Method**: GET
- **Headers**: CORS enabled, cache 1 hour
- **Media Type**: text/plain cho .obj files

### 2. `/download/{file_path:path}`
- **Mục đích**: Download file (giữ nguyên chức năng cũ)
- **Method**: GET
- **Headers**: Content-Disposition attachment

### 3. `/test-3d-viewer`
- **Mục đích**: Trang test riêng biệt cho 3D viewer
- **Method**: GET
- **Response**: HTML test page

## Cách sử dụng

### 1. Sử dụng trong giao diện chính
1. Mở ứng dụng tại `http://localhost:8080`
2. Tạo yêu cầu CAD (ví dụ: "Create a cube with dimensions 10x10x10")
3. Đợi quá trình generation hoàn thành
4. Model 3D sẽ tự động hiển thị trong panel "3D Model Viewer"
5. Sử dụng chuột để tương tác:
   - **Left click + drag**: Rotate
   - **Right click + drag**: Pan
   - **Scroll wheel**: Zoom

### 2. Sử dụng trang test
1. Truy cập `http://localhost:8080/test-3d-viewer`
2. Click "Load Test Cube" để load model test
3. Thử nghiệm các chức năng control

## Tích hợp kỹ thuật

### 1. Frontend Integration
```javascript
// Khởi tạo viewer
const modelViewer = new ModelViewer3D('viewer-3d-container');

// Load model
modelViewer.loadModel(objUrl, fileName)
  .then(success => {
    if (success) {
      console.log('Model loaded successfully');
    }
  });
```

### 2. Backend Integration
```python
# Trong response từ CAD generation
{
  "obj_export": "outputs/obj/model_name.obj",
  "step_export": "outputs/cad/model_name.step",
  "code": "# FreeCAD code...",
  "chat_response": "Model created successfully"
}
```

### 3. Workflow Integration
1. User gửi yêu cầu CAD
2. Server xử lý và tạo file .obj
3. Response trả về đường dẫn file
4. Frontend tự động load model vào 3D viewer
5. User có thể tương tác với model ngay lập tức

## Bảo mật và hiệu suất

### 1. Bảo mật
- CORS headers được cấu hình đúng
- File serving chỉ cho phép truy cập file trong thư mục outputs
- Validation đường dẫn file để tránh path traversal

### 2. Hiệu suất
- Cache file trong 1 giờ
- Lazy loading - chỉ load khi cần
- Optimized Three.js rendering
- File .obj được serve với media type phù hợp

### 3. Xử lý lỗi
- Graceful fallback khi không load được model
- Error logging chi tiết
- User-friendly error messages

## Troubleshooting

### 1. Model không hiển thị
- Kiểm tra console browser để xem lỗi
- Verify file .obj tồn tại trong outputs/obj/
- Kiểm tra CORS headers

### 2. Performance issues
- Kiểm tra kích thước file .obj
- Monitor memory usage trong browser
- Optimize model complexity nếu cần

### 3. Browser compatibility
- Requires modern browser với WebGL support
- Tested trên Chrome, Firefox, Safari, Edge

## Phát triển tiếp theo

### 1. Tính năng có thể thêm
- Support cho file .mtl (materials)
- Lighting controls
- Animation support
- Multiple model loading
- Model comparison view

### 2. Optimizations
- Progressive loading cho file lớn
- WebWorker cho parsing
- Texture support
- LOD (Level of Detail) system

### 3. Integration improvements
- Real-time collaboration
- Model annotation
- Measurement tools
- Export to other formats

## Kết luận

Hệ thống 3D viewer đã được tích hợp thành công, cung cấp trải nghiệm xem model 3D trực tiếp trên web mà không cần ứng dụng desktop riêng biệt. Điều này cải thiện đáng kể workflow và user experience của hệ thống CAD chatbot.
