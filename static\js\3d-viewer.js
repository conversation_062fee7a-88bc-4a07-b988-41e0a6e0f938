/**
 * 3D Model Viewer using Three.js
 * Handles loading and displaying OBJ files in the web interface
 */

class ModelViewer3D {
    constructor(containerId) {
        this.container = document.getElementById(containerId);
        this.scene = null;
        this.camera = null;
        this.renderer = null;
        this.controls = null;
        this.currentModel = null;
        this.isWireframe = false;
        this.modelInfo = {
            vertices: 0,
            faces: 0,
            fileName: ''
        };
        
        this.init();
        this.setupEventListeners();
    }

    init() {
        // Create scene
        this.scene = new THREE.Scene();
        this.scene.background = new THREE.Color(0xf0f0f0);

        // Create camera
        this.camera = new THREE.PerspectiveCamera(
            75,
            this.container.clientWidth / this.container.clientHeight,
            0.1,
            1000
        );
        this.camera.position.set(5, 5, 5);

        // Create renderer
        this.renderer = new THREE.WebGLRenderer({ antialias: true });
        this.renderer.setSize(this.container.clientWidth, this.container.clientHeight);
        this.renderer.shadowMap.enabled = true;
        this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;

        // Add renderer to container
        const viewerElement = this.container.querySelector('.viewer-3d');
        viewerElement.appendChild(this.renderer.domElement);

        // Create controls
        this.controls = new THREE.OrbitControls(this.camera, this.renderer.domElement);
        this.controls.enableDamping = true;
        this.controls.dampingFactor = 0.05;
        this.controls.enableZoom = true;
        this.controls.enablePan = true;

        // Add lights
        this.setupLighting();

        // Start render loop
        this.animate();

        // Handle window resize
        window.addEventListener('resize', () => this.onWindowResize());
    }

    setupLighting() {
        // Ambient light
        const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
        this.scene.add(ambientLight);

        // Directional light
        const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
        directionalLight.position.set(10, 10, 5);
        directionalLight.castShadow = true;
        directionalLight.shadow.mapSize.width = 2048;
        directionalLight.shadow.mapSize.height = 2048;
        this.scene.add(directionalLight);

        // Additional lights for better visibility
        const light1 = new THREE.DirectionalLight(0xffffff, 0.3);
        light1.position.set(-10, -10, -5);
        this.scene.add(light1);

        const light2 = new THREE.DirectionalLight(0xffffff, 0.3);
        light2.position.set(0, 10, -10);
        this.scene.add(light2);
    }

    setupEventListeners() {
        // Reset camera button
        document.getElementById('reset-camera-btn')?.addEventListener('click', () => {
            this.resetCamera();
        });

        // Wireframe toggle button
        document.getElementById('wireframe-toggle-btn')?.addEventListener('click', () => {
            this.toggleWireframe();
        });

        // Download OBJ button
        document.getElementById('download-obj-btn')?.addEventListener('click', () => {
            this.downloadCurrentModel();
        });

        // Fullscreen button
        document.getElementById('fullscreen-viewer-btn')?.addEventListener('click', () => {
            this.toggleFullscreen();
        });

        // Code toggle button
        document.getElementById('toggle-code-btn')?.addEventListener('click', () => {
            this.toggleCodeOutput();
        });
    }

    async loadOBJFile(objUrl, fileName = '') {
        try {
            this.showLoading(true);
            this.hideControls();

            // Remove previous model
            if (this.currentModel) {
                this.scene.remove(this.currentModel);
            }

            // Load OBJ file
            const loader = new THREE.OBJLoader();
            
            return new Promise((resolve, reject) => {
                loader.load(
                    objUrl,
                    (object) => {
                        this.currentModel = object;
                        
                        // Apply default material
                        object.traverse((child) => {
                            if (child.isMesh) {
                                child.material = new THREE.MeshLambertMaterial({
                                    color: 0x888888,
                                    side: THREE.DoubleSide
                                });
                                child.castShadow = true;
                                child.receiveShadow = true;
                            }
                        });

                        // Add to scene
                        this.scene.add(object);

                        // Center and scale the model
                        this.centerAndScaleModel(object);

                        // Update model info
                        this.updateModelInfo(object, fileName);

                        // Show controls and info
                        this.showControls();
                        this.showInfo();
                        this.hideLoading();

                        console.log('3D model loaded successfully:', fileName);
                        resolve(object);
                    },
                    (progress) => {
                        console.log('Loading progress:', (progress.loaded / progress.total * 100) + '%');
                    },
                    (error) => {
                        console.error('Error loading OBJ file:', error);
                        this.hideLoading();
                        this.showError('Failed to load 3D model');
                        reject(error);
                    }
                );
            });
        } catch (error) {
            console.error('Error in loadOBJFile:', error);
            this.hideLoading();
            this.showError('Failed to load 3D model');
            throw error;
        }
    }

    centerAndScaleModel(object) {
        // Calculate bounding box
        const box = new THREE.Box3().setFromObject(object);
        const center = box.getCenter(new THREE.Vector3());
        const size = box.getSize(new THREE.Vector3());

        // Center the model
        object.position.sub(center);

        // Scale the model to fit in view
        const maxDim = Math.max(size.x, size.y, size.z);
        const scale = 4 / maxDim; // Adjust this value to change default size
        object.scale.setScalar(scale);

        // Reset camera position
        this.resetCamera();
    }

    updateModelInfo(object, fileName) {
        let vertices = 0;
        let faces = 0;

        object.traverse((child) => {
            if (child.isMesh && child.geometry) {
                if (child.geometry.attributes.position) {
                    vertices += child.geometry.attributes.position.count;
                }
                if (child.geometry.index) {
                    faces += child.geometry.index.count / 3;
                } else if (child.geometry.attributes.position) {
                    faces += child.geometry.attributes.position.count / 3;
                }
            }
        });

        this.modelInfo = {
            vertices: Math.floor(vertices),
            faces: Math.floor(faces),
            fileName: fileName || 'model.obj'
        };

        this.updateInfoDisplay();
    }

    updateInfoDisplay() {
        const infoElement = document.getElementById('model-info-text');
        if (infoElement) {
            infoElement.innerHTML = `
                <strong>${this.modelInfo.fileName}</strong><br>
                Vertices: ${this.modelInfo.vertices.toLocaleString()}<br>
                Faces: ${this.modelInfo.faces.toLocaleString()}
            `;
        }
    }

    resetCamera() {
        if (this.currentModel) {
            const box = new THREE.Box3().setFromObject(this.currentModel);
            const size = box.getSize(new THREE.Vector3());
            const maxDim = Math.max(size.x, size.y, size.z);
            
            this.camera.position.set(maxDim * 1.5, maxDim * 1.5, maxDim * 1.5);
            this.camera.lookAt(0, 0, 0);
            this.controls.target.set(0, 0, 0);
            this.controls.update();
        }
    }

    toggleWireframe() {
        if (!this.currentModel) return;

        this.isWireframe = !this.isWireframe;
        
        this.currentModel.traverse((child) => {
            if (child.isMesh) {
                child.material.wireframe = this.isWireframe;
            }
        });

        // Update button appearance
        const btn = document.getElementById('wireframe-toggle-btn');
        if (btn) {
            btn.style.background = this.isWireframe ? 'rgba(59, 130, 246, 0.9)' : 'rgba(255, 255, 255, 0.9)';
            btn.style.color = this.isWireframe ? 'white' : '#374151';
        }
    }

    downloadCurrentModel() {
        // This would trigger download of the current OBJ file
        // Implementation depends on how files are stored/served
        if (this.modelInfo.fileName) {
            const downloadUrl = `/download/outputs/obj/${this.modelInfo.fileName}`;
            const link = document.createElement('a');
            link.href = downloadUrl;
            link.download = this.modelInfo.fileName;
            link.click();
        }
    }

    toggleFullscreen() {
        // Implementation for fullscreen mode
        const container = this.container;
        if (!document.fullscreenElement) {
            container.requestFullscreen().then(() => {
                this.onWindowResize();
            });
        } else {
            document.exitFullscreen();
        }
    }

    toggleCodeOutput() {
        const container = document.getElementById('code-output-container');
        const icon = document.querySelector('#toggle-code-btn i.fa-chevron-down');
        
        if (container.classList.contains('hidden')) {
            container.classList.remove('hidden');
            icon.style.transform = 'rotate(180deg)';
        } else {
            container.classList.add('hidden');
            icon.style.transform = 'rotate(0deg)';
        }
    }

    showLoading(show = true) {
        const loading = document.getElementById('viewer-loading');
        const placeholder = this.container.querySelector('.viewer-placeholder');
        
        if (show) {
            loading?.classList.remove('hidden');
            placeholder?.classList.add('hidden');
        } else {
            loading?.classList.add('hidden');
        }
    }

    hideLoading() {
        this.showLoading(false);
    }

    showControls() {
        document.getElementById('viewer-controls')?.classList.remove('hidden');
        document.getElementById('fullscreen-viewer-btn')?.classList.remove('hidden');
    }

    hideControls() {
        document.getElementById('viewer-controls')?.classList.add('hidden');
        document.getElementById('fullscreen-viewer-btn')?.classList.add('hidden');
    }

    showInfo() {
        document.getElementById('viewer-info')?.classList.remove('hidden');
    }

    showError(message) {
        const placeholder = this.container.querySelector('.viewer-placeholder');
        if (placeholder) {
            placeholder.innerHTML = `
                <i class="fas fa-exclamation-triangle"></i>
                <p class="text-sm">${message}</p>
                <p class="text-xs opacity-75 mt-1">Please try generating the model again</p>
            `;
            placeholder.classList.remove('hidden');
        }
    }

    onWindowResize() {
        if (!this.camera || !this.renderer) return;

        this.camera.aspect = this.container.clientWidth / this.container.clientHeight;
        this.camera.updateProjectionMatrix();
        this.renderer.setSize(this.container.clientWidth, this.container.clientHeight);
    }

    animate() {
        requestAnimationFrame(() => this.animate());
        
        if (this.controls) {
            this.controls.update();
        }
        
        if (this.renderer && this.scene && this.camera) {
            this.renderer.render(this.scene, this.camera);
        }
    }

    // Public method to load a model from URL
    async loadModel(objUrl, fileName) {
        try {
            await this.loadOBJFile(objUrl, fileName);
            return true;
        } catch (error) {
            console.error('Failed to load model:', error);
            return false;
        }
    }

    // Clear the current model
    clearModel() {
        if (this.currentModel) {
            this.scene.remove(this.currentModel);
            this.currentModel = null;
        }
        
        this.hideControls();
        document.getElementById('viewer-info')?.classList.add('hidden');
        
        const placeholder = this.container.querySelector('.viewer-placeholder');
        if (placeholder) {
            placeholder.innerHTML = `
                <i class="fas fa-cube"></i>
                <p class="text-sm">3D model will appear here</p>
                <p class="text-xs opacity-75 mt-1">Generate a model to see the preview</p>
            `;
            placeholder.classList.remove('hidden');
        }
    }
}

// Export for use in main.js
window.ModelViewer3D = ModelViewer3D;
