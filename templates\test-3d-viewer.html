<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>3D Viewer Test</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/three@0.128.0/examples/js/loaders/OBJLoader.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/three@0.128.0/examples/js/controls/OrbitControls.js"></script>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #f0f0f0;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .viewer-container {
            width: 100%;
            height: 500px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 8px;
            position: relative;
            overflow: hidden;
        }
        .controls {
            margin: 20px 0;
            text-align: center;
        }
        button {
            margin: 0 10px;
            padding: 10px 20px;
            background: #667eea;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        button:hover {
            background: #5a67d8;
        }
        .info {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>3D Model Viewer Test</h1>
        
        <div class="info">
            <h3>Test Instructions:</h3>
            <p>1. Click "Load Test Cube" to load a simple test model</p>
            <p>2. Use mouse to rotate, zoom, and pan the 3D model</p>
            <p>3. Use the control buttons to test various features</p>
        </div>

        <div class="controls">
            <button onclick="loadTestCube()">Load Test Cube</button>
            <button onclick="resetCamera()">Reset Camera</button>
            <button onclick="toggleWireframe()">Toggle Wireframe</button>
            <button onclick="clearModel()">Clear Model</button>
        </div>

        <div id="viewer-container" class="viewer-container">
            <div id="placeholder" style="display: flex; align-items: center; justify-content: center; height: 100%; color: white; text-align: center;">
                <div>
                    <h2>3D Model Viewer</h2>
                    <p>Click "Load Test Cube" to start</p>
                </div>
            </div>
        </div>

        <div class="info">
            <h3>Model Information:</h3>
            <div id="model-info">No model loaded</div>
        </div>
    </div>

    <script src="/static/js/3d-viewer.js"></script>
    <script>
        let viewer = null;

        // Initialize viewer when page loads
        document.addEventListener('DOMContentLoaded', function() {
            try {
                viewer = new ModelViewer3D('viewer-container');
                console.log('3D Viewer initialized successfully');
                
                // Hide placeholder when viewer is ready
                document.getElementById('placeholder').style.display = 'none';
            } catch (error) {
                console.error('Failed to initialize 3D viewer:', error);
                document.getElementById('placeholder').innerHTML = `
                    <div>
                        <h2>Error</h2>
                        <p>Failed to initialize 3D viewer: ${error.message}</p>
                    </div>
                `;
            }
        });

        function loadTestCube() {
            if (!viewer) {
                alert('3D Viewer not initialized');
                return;
            }

            const objUrl = '/api/3d-viewer/outputs/obj/test_cube.obj';
            const fileName = 'test_cube.obj';

            console.log('Loading test cube from:', objUrl);

            viewer.loadModel(objUrl, fileName)
                .then((success) => {
                    if (success) {
                        console.log('Test cube loaded successfully');
                        updateModelInfo('Test Cube', 'Loaded successfully');
                    } else {
                        console.error('Failed to load test cube');
                        updateModelInfo('Error', 'Failed to load test cube');
                    }
                })
                .catch((error) => {
                    console.error('Error loading test cube:', error);
                    updateModelInfo('Error', error.message);
                });
        }

        function resetCamera() {
            if (viewer) {
                viewer.resetCamera();
            }
        }

        function toggleWireframe() {
            if (viewer) {
                viewer.toggleWireframe();
            }
        }

        function clearModel() {
            if (viewer) {
                viewer.clearModel();
                updateModelInfo('No model loaded', '');
            }
        }

        function updateModelInfo(name, status) {
            const infoDiv = document.getElementById('model-info');
            infoDiv.innerHTML = `
                <strong>Model:</strong> ${name}<br>
                <strong>Status:</strong> ${status}
            `;
        }
    </script>
</body>
</html>
